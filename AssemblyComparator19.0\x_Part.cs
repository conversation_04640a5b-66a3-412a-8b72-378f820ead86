﻿// Decompiled with JetBrains decompiler
// Type: AssemblyComparator.x_Part
// Assembly: AssemblyComparator_181, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: DFC38E62-58C3-4AD4-9913-BA3E15B1A611
// Assembly location: C:\Users\<USER>\Desktop\AssemblyComparator19.0.exe

using Tekla.Structures.Geometry3d;
using Tekla.Structures.Model;

#nullable disable
namespace AssemblyComparator
{
  public struct x_Part
  {
    public Part Ref;
    public string Pos;
    public Point COG;
    public double distCOG;
    public bool Have;
    public string msg;
  }
}
