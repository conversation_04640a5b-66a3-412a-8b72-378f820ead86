{"format": 1, "restore": {"c:\\Users\\<USER>\\Desktop\\1\\AssemblyComparator19.0\\AssemblyComparator19.0.csproj": {}}, "projects": {"c:\\Users\\<USER>\\Desktop\\1\\AssemblyComparator19.0\\AssemblyComparator19.0.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\Desktop\\1\\AssemblyComparator19.0\\AssemblyComparator19.0.csproj", "projectName": "AssemblyComparator19.0", "projectPath": "c:\\Users\\<USER>\\Desktop\\1\\AssemblyComparator19.0\\AssemblyComparator19.0.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\Desktop\\1\\AssemblyComparator19.0\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"System.Resources.Extensions": {"target": "Package", "version": "[8.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}