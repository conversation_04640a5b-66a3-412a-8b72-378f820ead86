﻿// Decompiled with JetBrains decompiler
// Type: AssemblyComparator.Properties.Settings
// Assembly: AssemblyComparator_181, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: DFC38E62-58C3-4AD4-9913-BA3E15B1A611
// Assembly location: C:\Users\<USER>\Desktop\AssemblyComparator19.0.exe

using System.CodeDom.Compiler;
using System.Configuration;
using System.Runtime.CompilerServices;

#nullable disable
namespace AssemblyComparator.Properties
{
  [GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "10.0.0.0")]
  [CompilerGenerated]
  internal sealed class Settings : ApplicationSettingsBase
  {
    private static Settings defaultInstance = (Settings) SettingsBase.Synchronized((SettingsBase) new Settings());

    public static Settings Default => Settings.defaultInstance;
  }
}
