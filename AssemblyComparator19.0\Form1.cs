// Decompiled with JetBrains decompiler
// Type: AssemblyComparator.Form1
// Assembly: AssemblyComparator_181, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: DFC38E62-58C3-4AD4-9913-BA3E15B1A611
// Assembly location: C:\Users\<USER>\Desktop\AssemblyComparator19.0.exe

using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using Tekla.Structures.Geometry3d;
using Tekla.Structures.Model;


namespace AssemblyComparator
{
  public class Form1 : Form
  {
    private Tekla.Structures.Model.Model fld_model;
    private ArrayList fld_refList;
    private IContainer components;
    private Button button_close;
    private Button button_run;
    private DataGridView dataGridView1;
    private DataGridViewTextBoxColumn Column1;
    private DataGridViewTextBoxColumn Column2;
    private DataGridViewTextBoxColumn Column3;

    public Form1() => this.InitializeComponent();

    private void button_close_Click(object sender, EventArgs e) => this.Close();



    private void button_run_Click(object sender, EventArgs e)
    {
      try
      {
        this.Compare();
      }
      catch
      {
        int num = (int) MessageBox.Show("Tekla还没有运行，或者Tekla的版本不是本工具版本");
      }
    }

    private void Compare()
    {
      this.fld_model = new Tekla.Structures.Model.Model();
      if (!this.fld_model.GetConnectionStatus())
      {
        int num1 = (int) MessageBox.Show("Tekla还没有运行，或者Tekla的版本不是本工具版本");
      }
      else
      {
        this.Enabled = false;
        ModelObjectEnumerator selectedObjects = new Tekla.Structures.Model.UI.ModelObjectSelector().GetSelectedObjects();
        List<x_Assembly> xAssemblyList = new List<x_Assembly>();
        int num2 = 0;
        while (selectedObjects.MoveNext() && num2 != 2)
        {
          if (selectedObjects.Current is Assembly current2)
          {
            ++num2;
            xAssemblyList.Add(this.GetAssembly(this.fld_model, current2));
          }
          else if (selectedObjects.Current is Part current1)
          {
            ++num2;
            xAssemblyList.Add(this.GetAssembly(this.fld_model, current1.GetAssembly()));
          }
        }
        if (xAssemblyList.Count != 2)
        {
          int num3 = (int) MessageBox.Show("You should select two parts or assemblies.");
          this.Enabled = true;
        }
        else
        {
          this.dataGridView1.Columns[0].HeaderText = xAssemblyList[0].Pos;
          this.dataGridView1.Columns[1].HeaderText = xAssemblyList[1].Pos;
          this.dataGridView1.Rows.Clear();
          this.fld_refList = new ArrayList();
          if (xAssemblyList[0].Main.Pos != xAssemblyList[1].Main.Pos)
          {
            this.fld_refList.Add((object) new ArrayList()
            {
              (object) xAssemblyList[0].Main.Ref,
              (object) xAssemblyList[1].Main.Ref
            });
            this.AddRow(xAssemblyList[0].Main.Pos, xAssemblyList[1].Main.Pos, "part position");
            this.Enabled = true;
          }
          else
          {
            for (int index1 = 0; index1 < xAssemblyList[0].PartList.Count; ++index1)
            {
              x_Part part1 = xAssemblyList[0].PartList[index1];
              for (int index2 = 0; index2 < xAssemblyList[1].PartList.Count; ++index2)
              {
                x_Part part2 = xAssemblyList[1].PartList[index2];
                if (!part2.Have && this.CompareDist(part1.distCOG, part2.distCOG))
                {
                  if (part1.Pos == part2.Pos)
                  {
                    part1.Have = true;
                    part2.Have = true;
                  }
                  xAssemblyList[0].PartList[index1] = part1;
                  xAssemblyList[1].PartList[index2] = part2;
                  break;
                }
              }
            }
            List<x_Part> xPartList1 = this.LostPart(xAssemblyList[0]);
            List<x_Part> xPartList2 = this.LostPart(xAssemblyList[1]);
            if (xPartList1.Count < xPartList2.Count)
            {
              List<x_Part> xPartList3 = xPartList1;
              xPartList1 = xPartList2;
              xPartList2 = xPartList3;
            }
            for (int index3 = 0; index3 < xPartList1.Count; ++index3)
            {
              string str3 = xPartList1[index3].msg;
              string str2 = "";
              ModelObject modelObject = (ModelObject) null;
              int index4 = -1;
              double num4 = 50.0;
              ArrayList arrayList = new ArrayList();
              for (int index5 = 0; index5 < xPartList2.Count; ++index5)
              {
                double num5 = Math.Abs(xPartList1[index3].distCOG - xPartList2[index5].distCOG);
                if (num5 < num4)
                {
                  num4 = num5;
                  str2 = xPartList2[index5].Pos;
                  modelObject = (ModelObject) xPartList2[index5].Ref;
                  index4 = index5;
                }
              }
              arrayList.Add((object) xPartList1[index3].Ref);
              if (modelObject != null)
                arrayList.Add((object) modelObject);
              else
                str3 = "количеством";
              this.fld_refList.Add((object) arrayList);
              this.AddRow(xPartList1[index3].Pos, str2, str3);
              if (index4 != -1)
                xPartList2.RemoveAt(index4);
            }
            for (int index = 0; index < xPartList2.Count; ++index)
            {
              x_Part xPart = xPartList2[index];
              this.fld_refList.Add((object) new ArrayList()
              {
                (object) xPart.Ref
              });
              this.AddRow(xPart.Pos, "", "quantity");
            }
            this.Enabled = true;
          }
        }
      }
    }

    private void AddRow(string str1, string str2, string str3)
    {
      int count = this.dataGridView1.Rows.Count;
      this.dataGridView1.Rows.Add();
      this.dataGridView1.Rows[count].HeaderCell.Value = (object) count.ToString();
      this.dataGridView1.Rows[count].Cells[0].Value = (object) str1;
      this.dataGridView1.Rows[count].Cells[1].Value = (object) str2;
    }

    private x_Assembly GetAssembly(Tekla.Structures.Model.Model model, Assembly ass)
    {
      x_Assembly assembly = new x_Assembly();
      assembly.PartList = new List<x_Part>();
      Part part1 = this.fld_model.SelectModelObject((ass.GetMainPart() as Part).Identifier) as Part;
      string str1 = "";
      string str2 = "";
      double X = 0.0;
      double Y = 0.0;
      double Z = 0.0;
      ass.GetReportProperty("ASSEMBLY_POS", ref str1);
      part1.GetReportProperty("PART_POS", ref str2);
      part1.GetReportProperty("COG_X", ref X);
      part1.GetReportProperty("COG_Y", ref Y);
      part1.GetReportProperty("COG_Z", ref Z);
      assembly.Pos = str1;
      assembly.Main = new x_Part();
      assembly.Main.Ref = part1;
      assembly.Main.Pos = str2;
      assembly.Main.COG = new Tekla.Structures.Geometry3d.Point(X, Y, Z);
      assembly.Main.Have = false;
      assembly.Main.msg = "location";
      foreach (Tekla.Structures.Model.Object secondary in ass.GetSecondaries())
      {
        Part part2 = this.fld_model.SelectModelObject(secondary.Identifier) as Part;
        part2.GetReportProperty("PART_POS", ref str2);
        part2.GetReportProperty("COG_X", ref X);
        part2.GetReportProperty("COG_Y", ref Y);
        part2.GetReportProperty("COG_Z", ref Z);
        x_Part xPart = new x_Part()
        {
          Ref = part2,
          Pos = str2,
          COG = new Tekla.Structures.Geometry3d.Point(X, Y, Z)
        };
        xPart.distCOG = Distance.PointToPoint(xPart.COG, assembly.Main.COG);
        xPart.Have = false;
        xPart.msg = "location";
        assembly.PartList.Add(xPart);
      }
      return assembly;
    }

    private bool ComparePoints(Tekla.Structures.Geometry3d.Point p1, Tekla.Structures.Geometry3d.Point p2)
    {
      double num = 0.5;
      return Math.Abs(p1.X - p2.X) <= num && Math.Abs(p1.Y - p2.Y) <= num && Math.Abs(p1.Z - p2.Z) <= num;
    }

    private bool CompareDist(double d1, double d2)
    {
      double num = 0.005;
      return Math.Abs(d1 - d2) <= num;
    }

    private List<x_Part> LostPart(x_Assembly ass)
    {
      List<x_Part> xPartList = new List<x_Part>();
      for (int index = 0; index < ass.PartList.Count; ++index)
      {
        x_Part part = ass.PartList[index];
        if (!part.Have)
          xPartList.Add(part);
      }
      return xPartList;
    }

    private void Sort(List<x_Part> list)
    {
      int num = list.Count - 1;
      if (num == 1)
      {
        if (list[0].distCOG <= list[1].distCOG)
          return;
        x_Part xPart = new x_Part();
        xPart.Ref = list[0].Ref;
        xPart.Pos = list[0].Pos;
        xPart.COG = list[0].COG;
        xPart.distCOG = list[0].distCOG;
        xPart.msg = list[0].msg;
        list[0] = list[1];
        list[1] = xPart;
      }
      else
      {
        for (int index1 = 0; index1 < num - 1; ++index1)
        {
          for (int index2 = 0; index2 < num - index1; ++index2)
          {
            x_Part xPart1 = list[index2];
            x_Part xPart2 = list[index2 + 1];
            if (xPart1.distCOG > xPart2.distCOG)
            {
              x_Part xPart3 = new x_Part();
              xPart3.Ref = xPart1.Ref;
              xPart3.Pos = xPart1.Pos;
              xPart3.COG = xPart1.COG;
              xPart3.distCOG = xPart1.distCOG;
              xPart3.msg = xPart1.msg;
              list[index2] = xPart2;
              list[index2 + 1] = xPart3;
            }
          }
        }
      }
    }

    private void dataGridView1_RowHeaderMouseClick(object sender, DataGridViewCellMouseEventArgs e)
    {
      new Tekla.Structures.Model.UI.ModelObjectSelector().Select(this.fld_refList[e.RowIndex] as ArrayList);
    }

    private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
    {
      if (e.ColumnIndex < 0 || e.RowIndex < 0)
        return;
      Tekla.Structures.Model.UI.ModelObjectSelector modelObjectSelector = new Tekla.Structures.Model.UI.ModelObjectSelector();
      ArrayList fldRef = this.fld_refList[e.RowIndex] as ArrayList;
      if (e.ColumnIndex + 1 > fldRef.Count)
        return;
      modelObjectSelector.Select(new ArrayList()
      {
        fldRef[e.ColumnIndex]
      });
    }

    protected override void Dispose(bool disposing)
    {
      if (disposing && this.components != null)
        this.components.Dispose();
      base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
      this.components = new System.ComponentModel.Container();
      ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (Form1));
      this.button_close = new Button();
      this.button_run = new Button();
      this.dataGridView1 = new DataGridView();
      this.Column1 = new DataGridViewTextBoxColumn();
      this.Column2 = new DataGridViewTextBoxColumn();
      this.Column3 = new DataGridViewTextBoxColumn();
      ((ISupportInitialize) this.dataGridView1).BeginInit();
      this.SuspendLayout();
      this.button_close.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
      this.button_close.Location = new System.Drawing.Point(213, 227);
      this.button_close.Name = "button_close";
      this.button_close.Size = new Size(75, 23);
      this.button_close.TabIndex = 26;
      this.button_close.Text = "退出";
      this.button_close.UseVisualStyleBackColor = true;
      this.button_close.Click += new EventHandler(this.button_close_Click);
      this.button_run.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
      this.button_run.Location = new System.Drawing.Point(12, 227);
      this.button_run.Name = "button_run";
      this.button_run.Size = new Size(75, 23);
      this.button_run.TabIndex = 25;
      this.button_run.Text = "比较";
      this.button_run.UseVisualStyleBackColor = true;
      this.button_run.Click += new EventHandler(this.button_run_Click);
      this.dataGridView1.AllowUserToAddRows = false;
      this.dataGridView1.AllowUserToDeleteRows = false;
      this.dataGridView1.AllowUserToResizeColumns = false;
      this.dataGridView1.AllowUserToResizeRows = false;
      this.dataGridView1.BackgroundColor = SystemColors.Control;
      this.dataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
      this.dataGridView1.Columns.AddRange((DataGridViewColumn) this.Column1, (DataGridViewColumn) this.Column2, (DataGridViewColumn) this.Column3);
      this.dataGridView1.Location = new System.Drawing.Point(12, 12);
      this.dataGridView1.MultiSelect = false;
      this.dataGridView1.Name = "dataGridView1";
      this.dataGridView1.ReadOnly = true;
      this.dataGridView1.RowHeadersWidth = 50;
      this.dataGridView1.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
      this.dataGridView1.Size = new Size(276, 209);
      this.dataGridView1.TabIndex = 27;
      this.dataGridView1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
      this.dataGridView1.CellClick += new DataGridViewCellEventHandler(this.dataGridView1_CellClick);
      this.dataGridView1.RowHeaderMouseClick += new DataGridViewCellMouseEventHandler(this.dataGridView1_RowHeaderMouseClick);
      this.Column1.HeaderText = "构件1";
      this.Column1.Name = "Column1";
      this.Column1.ReadOnly = true;
      this.Column1.Resizable = DataGridViewTriState.False;
      this.Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column2.HeaderText = "构件2";
      this.Column2.Name = "Column2";
      this.Column2.ReadOnly = true;
      this.Column2.Resizable = DataGridViewTriState.False;
      this.Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column3.HeaderText = "Отличаються:";
      this.Column3.Name = "Column3";
      this.Column3.ReadOnly = true;
      this.Column3.Resizable = DataGridViewTriState.False;
      this.Column3.SortMode = DataGridViewColumnSortMode.NotSortable;
      this.Column3.Visible = false;
      this.Column3.Width = 150;
      this.AutoScaleDimensions = new SizeF(6f, 13f);
      this.AutoScaleMode = AutoScaleMode.Font;
      this.ClientSize = new Size(300, 262);
      this.Controls.Add((Control) this.dataGridView1);
      this.Controls.Add((Control) this.button_close);
      this.Controls.Add((Control) this.button_run);
      this.FormBorderStyle = FormBorderStyle.FixedDialog;
      this.Icon = (Icon) componentResourceManager.GetObject("$this.Icon");
      this.MaximizeBox = false;
      this.Name = nameof (Form1);
      this.Text = "Tekla 构件比较_19.0版_(QQ群:2308380)";
      this.TopMost = true;
      ((ISupportInitialize) this.dataGridView1).EndInit();
      this.ResumeLayout(false);
      this.PerformLayout();
    }
  }
}
