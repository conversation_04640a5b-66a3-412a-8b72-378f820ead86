﻿using System.Reflection;
using System.Runtime.InteropServices;

[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyTitle("AssemblyComparator")]
[assembly: AssemblyProduct("AssemblyComparator")]
[assembly: AssemblyCopyright("")]
[assembly: ComVisible(false)]
[assembly: Guid("8d12d0a4-c41b-4233-8729-93b8a303a31c")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyVersion("*******")]
