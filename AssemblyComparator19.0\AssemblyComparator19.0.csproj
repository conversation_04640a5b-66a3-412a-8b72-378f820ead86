﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: C:\Users\<USER>\Desktop\AssemblyComparator19.0.exe-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6D2D1F0E-DC1C-4DA5-A83D-73EE48B016F4}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AssemblyName>AssemblyComparator_181</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <ApplicationVersion>1.0.0.0</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
    <RootNamespace>AssemblyComparator</RootNamespace>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="Tekla.Structures" />
    <Reference Include="Tekla.Structures.Model" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="x_Part.cs" />
    <Compile Include="x_Assembly.cs" />
    <Compile Include="Form1.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\Resources.cs" />
    <Compile Include="Properties\Settings.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Form1.resx" />
    <EmbeddedResource Include="Properties\Resources.resx" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>