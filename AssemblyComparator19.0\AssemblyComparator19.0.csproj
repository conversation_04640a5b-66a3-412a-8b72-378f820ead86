<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyName>AssemblyComparator19.0</AssemblyName>
    <RootNamespace>AssemblyComparator</RootNamespace>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Resources.Extensions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>

  <!-- Tekla API references -->
  <ItemGroup>
    <Reference Include="Tekla.Structures">
      <HintPath>C:\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="Tekla.Structures.Model">
      <HintPath>C:\TeklaStructures\19.0\nt\bin\plugins\Tekla.Structures.Model.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>

</Project>