<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyName>AssemblyComparator19.0</AssemblyName>
    <RootNamespace>AssemblyComparator</RootNamespace>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Resources.Extensions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>

  <!-- Tekla API references - these will need to be available locally -->
  <ItemGroup Condition="Exists('$(ProgramFiles)\Tekla Structures\')">
    <Reference Include="Tekla.Structures">
      <HintPath>$(ProgramFiles)\Tekla Structures\bin\Tekla.Structures.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="Tekla.Structures.Model">
      <HintPath>$(ProgramFiles)\Tekla Structures\bin\Tekla.Structures.Model.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <!-- Fallback for when Tekla is not installed - create stub interfaces -->
  <PropertyGroup Condition="!Exists('$(ProgramFiles)\Tekla Structures\')">
    <DefineConstants>$(DefineConstants);NO_TEKLA</DefineConstants>
  </PropertyGroup>

</Project>