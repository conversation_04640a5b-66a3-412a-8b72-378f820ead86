﻿// Decompiled with JetBrains decompiler
// Type: AssemblyComparator.Properties.Resources
// Assembly: AssemblyComparator_181, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: DFC38E62-58C3-4AD4-9913-BA3E15B1A611
// Assembly location: C:\Users\<USER>\Desktop\AssemblyComparator19.0.exe

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

#nullable disable
namespace AssemblyComparator.Properties
{
  [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
  [DebuggerNonUserCode]
  [CompilerGenerated]
  internal class Resources
  {
    private static ResourceManager resourceMan;
    private static CultureInfo resourceCulture;

    internal Resources()
    {
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
      get
      {
        if (object.ReferenceEquals((object) AssemblyComparator.Properties.Resources.resourceMan, (object) null))
          AssemblyComparator.Properties.Resources.resourceMan = new ResourceManager("AssemblyComparator.Properties.Resources", typeof (AssemblyComparator.Properties.Resources).Assembly);
        return AssemblyComparator.Properties.Resources.resourceMan;
      }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
      get => AssemblyComparator.Properties.Resources.resourceCulture;
      set => AssemblyComparator.Properties.Resources.resourceCulture = value;
    }
  }
}
